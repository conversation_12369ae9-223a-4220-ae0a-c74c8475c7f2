import { z } from 'zod';

const serverEnvSchema = z.object({
  // Database
  POSTGRES_URL: z.string().min(1),
  
  // Authentication
  AUTH_SECRET: z.string().min(1),
  
  // AI Providers
  OPENAI_API_KEY: z.string().optional(),
  ANTHROPIC_API_KEY: z.string().optional(),
  GOOGLE_GENERATIVE_AI_API_KEY: z.string().optional(),
  XAI_API_KEY: z.string().optional(),
  
  // Memory
  MEM0_API_KEY: z.string().optional(),
  MEM0_ORG_ID: z.string().optional(),
  MEM0_PROJECT_ID: z.string().optional(),
  
  // Redis
  REDIS_KV_URL: z.string().optional(),
  
  // Blob Storage
  BLOB_READ_WRITE_TOKEN: z.string().optional(),
  
  // Search APIs
  TAVILY_API_KEY: z.string().optional(),
  SERPER_API_KEY: z.string().optional(),
  
  // Extreme Search APIs
  EXA_API_KEY: z.string().optional(),
  DAYTONA_API_KEY: z.string().optional(),
  
  // Other
  FIRECRAWL_API_KEY: z.string().optional(),
  
  // Environment
  NODE_ENV: z.enum(['development', 'production', 'test']).default('development'),
});

export const serverEnv = serverEnvSchema.parse(process.env);
