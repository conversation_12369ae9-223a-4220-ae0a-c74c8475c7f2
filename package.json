{"name": "ai-chatbot", "version": "4.0.8", "private": true, "scripts": {"dev": "next dev --turbo", "build": "tsx lib/db/migrate && next build", "start": "next start", "lint": "next lint && biome lint --write --unsafe", "lint:fix": "next lint --fix && biome lint --write --unsafe", "format": "biome format --write", "db:generate": "drizzle-kit generate", "db:migrate": "npx tsx lib/db/migrate.ts", "db:studio": "drizzle-kit studio", "db:push": "drizzle-kit push", "db:pull": "drizzle-kit pull", "db:check": "drizzle-kit check", "db:up": "drizzle-kit up", "db:test-connection": "npx tsx scripts/test-db-connection.ts", "db:optimize": "npx tsx scripts/add-performance-indexes.ts", "test": "export PLAYWRIGHT=True && pnpm exec playwright test", "test:shard": "export PLAYWRIGHT=True && pnpm exec playwright test --shard={shard}/{total}"}, "dependencies": {"@ai-sdk/google": "^0.0.55", "@ai-sdk/react": "^1.2.12", "@ai-sdk/xai": "^1.2.10", "@auth/core": "^0.38.0", "@codemirror/lang-cpp": "^6.0.2", "@codemirror/lang-css": "^6.3.1", "@codemirror/lang-html": "^6.4.9", "@codemirror/lang-java": "^6.0.1", "@codemirror/lang-javascript": "^6.2.3", "@codemirror/lang-json": "^6.0.1", "@codemirror/lang-markdown": "^6.3.2", "@codemirror/lang-python": "^6.1.6", "@codemirror/lang-rust": "^6.0.1", "@codemirror/lang-sql": "^6.8.0", "@codemirror/language": "^6.11.0", "@codemirror/state": "^6.5.0", "@codemirror/theme-one-dark": "^6.1.2", "@codemirror/view": "^6.35.3", "@codesandbox/sandpack-react": "^2.6.9", "@codesandbox/sdk": "^0.12.0", "@mendable/firecrawl-js": "^1.24.0", "@phosphor-icons/react": "^2.1.7", "@radix-ui/react-alert-dialog": "^1.1.2", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.2", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.3", "@radix-ui/react-visually-hidden": "^1.1.0", "@ricky0123/vad-react": "^0.0.28", "@tailwindcss/line-clamp": "^0.4.4", "@tavily/core": "^0.3.3", "@types/react-syntax-highlighter": "^15.5.13", "@vercel/analytics": "^1.3.1", "@vercel/blob": "^0.24.1", "@vercel/functions": "^2.0.0", "@vercel/postgres": "^0.10.0", "ai": "4.3.4", "bcrypt-ts": "^5.0.2", "class-variance-authority": "^0.7.0", "classnames": "^2.5.1", "clsx": "^2.1.1", "codemirror": "^6.0.1", "date-fns": "^4.1.0", "diff-match-patch": "^1.0.5", "dotenv": "^16.5.0", "drizzle-orm": "^0.34.0", "echarts-for-react": "^3.0.2", "exa-js": "^1.8.20", "fast-deep-equal": "^3.1.3", "framer-motion": "^11.3.19", "geist": "^1.3.1", "groq-sdk": "^0.3.1", "lru-cache": "^10.2.0", "lucide-react": "^0.446.0", "mem0ai": "^2.1.16", "nanoid": "^5.0.8", "next": "15.3.0-canary.31", "next-auth": "5.0.0-beta.25", "next-themes": "^0.3.0", "orderedmap": "^2.1.1", "papaparse": "^5.5.2", "postgres": "^3.4.4", "prosemirror-example-setup": "^1.2.3", "prosemirror-inputrules": "^1.4.0", "prosemirror-markdown": "^1.13.1", "prosemirror-model": "^1.23.0", "prosemirror-schema-basic": "^1.2.3", "prosemirror-schema-list": "^1.4.1", "prosemirror-state": "^1.4.3", "prosemirror-view": "^1.34.3", "react": "19.0.0-rc-45804af1-20241021", "react-data-grid": "7.0.0-beta.47", "react-dom": "19.0.0-rc-45804af1-20241021", "react-markdown": "^9.0.1", "react-resizable-panels": "^2.1.7", "react-syntax-highlighter": "^15.6.1", "redis": "^4.7.0", "remark-gfm": "^4.0.0", "server-only": "^0.0.1", "sonner": "^1.5.0", "swr": "^2.2.5", "tailwind-merge": "^2.5.2", "tailwindcss-animate": "^1.0.7", "usehooks-ts": "^3.1.0", "vaul": "^1.1.2", "zod": "^3.23.8", "zustand": "^5.0.4"}, "devDependencies": {"@biomejs/biome": "1.9.4", "@playwright/test": "^1.50.1", "@tailwindcss/typography": "^0.5.15", "@types/d3-scale": "^4.0.8", "@types/node": "^22.8.6", "@types/papaparse": "^5.3.15", "@types/pdf-parse": "^1.1.4", "@types/react": "^18", "@types/react-dom": "^18", "autoprefixer": "^10.4.21", "drizzle-kit": "^0.25.0", "eslint": "^8.57.0", "eslint-config-next": "14.2.5", "eslint-config-prettier": "^9.1.0", "eslint-import-resolver-typescript": "^3.6.3", "eslint-plugin-tailwindcss": "^3.17.5", "postcss": "^8.5.3", "tailwindcss": "^3.4.17", "tsx": "^4.19.1", "typescript": "^5.6.3"}, "packageManager": "pnpm@9.12.3"}