# 🔍 Guide de la Recherche Extrême

## 📋 Vue d'ensemble

La **Recherche Extrême** est un système de recherche avancé intégré dans votre chatbot qui permet de :
- 🌐 Effectuer des recherches web approfondies avec l'API Exa
- 🤖 Utiliser des agents de recherche autonomes
- 📊 Analyser et synthétiser les informations trouvées
- 📈 Créer des visualisations (quand Daytona est configuré)

## ✅ Configuration Actuelle

### ✅ Fonctionnalités Activées
- **Recherche Web Avancée** : Utilise l'API Exa pour des recherches de haute qualité
- **Agent de Recherche Autonome** : Planifie et exécute automatiquement des recherches multiples
- **Interface Utilisateur** : Composant ExtremeSearchComponent intégré dans le chat
- **Sources et Annotations** : Affichage des sources et du processus de recherche

### ⚠️ Fonctionnalités Optionnelles
- **Exécution de Code Python** : Désactivée (nécessite Daytona API)
- **Visualisations** : Désactivées (nécessite Daytona API)

## 🚀 Comment Utiliser

### 1. Démarrer une Recherche Extrême
Dans le chat, posez des questions qui nécessitent une recherche approfondie :

```
"Recherche les dernières tendances en IA pour 2024"
"Analyse le marché des voitures électriques en Europe"
"Trouve des informations sur les nouvelles technologies blockchain"
```

### 2. Le Processus Automatique
L'assistant va automatiquement :
1. **Planifier** la recherche en décomposant votre question
2. **Exécuter** plusieurs recherches ciblées
3. **Analyser** les résultats trouvés
4. **Synthétiser** une réponse complète avec sources

### 3. Interface de Recherche
Vous verrez :
- 📋 **Plan de recherche** : Les étapes planifiées
- 🔍 **Requêtes en temps réel** : Les recherches en cours
- 📄 **Sources trouvées** : Les sites web consultés
- ✅ **Résultats** : La synthèse finale avec sources

## 🔧 Configuration Technique

### Variables d'Environnement Requises
```env
# Dans votre fichier .env
EXA_API_KEY=votre_cle_api_exa_ici
```

### Variables d'Environnement Optionnelles
```env
# Pour l'exécution de code (optionnel)
DAYTONA_API_KEY=votre_cle_daytona_ici
SNAPSHOT_NAME=nom_de_votre_snapshot_daytona
```

## 📁 Fichiers Intégrés

### Backend
- `lib/ai/tools/extreme-search.ts` : Logique principale de recherche
- `app/(chat)/api/chat/route.ts` : Intégration dans l'API de chat
- `env/server.ts` : Configuration des variables d'environnement

### Frontend
- `components/extreme-search.tsx` : Interface utilisateur
- `components/message.tsx` : Intégration dans les messages du chat

### Configuration
- `package.json` : Dépendance `exa-js` ajoutée
- `.env` : Variables d'environnement

## 🎯 Exemples d'Utilisation

### Recherche Technologique
```
"Recherche les dernières avancées en intelligence artificielle générative"
```

### Analyse de Marché
```
"Analyse les tendances du marché immobilier français en 2024"
```

### Recherche Académique
```
"Trouve des études récentes sur l'impact du télétravail sur la productivité"
```

### Actualités et Tendances
```
"Quelles sont les dernières nouvelles sur les cryptomonnaies ?"
```

## 🔍 Fonctionnalités Avancées

### Recherche Multi-Catégories
Le système utilise différentes catégories de recherche :
- `news` : Actualités récentes
- `company` : Informations d'entreprises
- `research paper` : Articles académiques
- `github` : Projets et code
- `financial report` : Rapports financiers

### Recherche Progressive
L'agent effectue des recherches progressives :
1. **Vue d'ensemble** : Recherche générale sur le sujet
2. **Détails spécifiques** : Recherches ciblées sur les aspects importants
3. **Développements récents** : Actualités et tendances
4. **Validation croisée** : Vérification des informations

## 🚀 Prochaines Étapes

Pour activer toutes les fonctionnalités :

1. **Obtenir une clé Daytona** (optionnel) :
   - Visitez [daytona.io](https://daytona.io)
   - Créez un compte et obtenez votre API key
   - Ajoutez `DAYTONA_API_KEY` dans `.env`

2. **Configurer un snapshot Daytona** (optionnel) :
   - Créez un environnement Python dans Daytona
   - Ajoutez `SNAPSHOT_NAME` dans `.env`

## ✅ Statut : Prêt à Utiliser !

Votre système de recherche extrême est maintenant **complètement fonctionnel** pour :
- ✅ Recherches web avancées
- ✅ Analyse automatique
- ✅ Interface utilisateur complète
- ✅ Sources et annotations

**Commencez dès maintenant** en posant une question de recherche dans le chat !
